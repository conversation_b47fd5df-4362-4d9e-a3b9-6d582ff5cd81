{"rustc": 11410426090777951712, "features": "[\"default\", \"extension-module\", \"indoc\", \"macros\", \"pyo3-macros\", \"unindent\"]", "declared_features": "[\"abi3\", \"abi3-py310\", \"abi3-py311\", \"abi3-py312\", \"abi3-py37\", \"abi3-py38\", \"abi3-py39\", \"anyhow\", \"auto-initialize\", \"chrono\", \"default\", \"either\", \"experimental-inspect\", \"extension-module\", \"eyre\", \"full\", \"generate-import-lib\", \"hashbrown\", \"indexmap\", \"indoc\", \"inventory\", \"macros\", \"multiple-pymethods\", \"nightly\", \"num-bigint\", \"num-complex\", \"pyo3-macros\", \"rust_decimal\", \"serde\", \"smallvec\", \"unindent\"]", "target": 11844115677734887287, "profile": 17744942687396784568, "path": 5136896698458190253, "deps": [[629381703529241162, "indoc", false, 1756258502519794776], [2828590642173593838, "cfg_if", false, 3259293046319211000], [3958489542916937055, "portable_atomic", false, 1693097779577518771], [4495526598637097934, "parking_lot", false, 16257243907029082278], [4684437522915235464, "libc", false, 3936220782303720335], [7614379793313454108, "pyo3_ffi", false, 13623881952775587947], [12478333222061076338, "pyo3_macros", false, 48504491450611431], [14643204177830147187, "memoffset", false, 15940153085630986796], [14748792705540276325, "unindent", false, 16092396467736160894], [17396333227350152061, "build_script_build", false, 3082072263595227992]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/pyo3-eb9520b18f5395aa/dep-lib-pyo3", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}