{"rustc": 11410426090777951712, "features": "[\"default\", \"extension-module\", \"resolve-config\"]", "declared_features": "[\"abi3\", \"abi3-py310\", \"abi3-py311\", \"abi3-py312\", \"abi3-py37\", \"abi3-py38\", \"abi3-py39\", \"default\", \"extension-module\", \"python3-dll-a\", \"resolve-config\"]", "target": 11662020830826191376, "profile": 2225463790103693989, "path": 15655328219508963769, "deps": [[3722963349756955755, "once_cell", false, 17804875059537849432], [10296317077653712691, "target_lexicon", false, 18233718267111416792], [11057642215492122650, "build_script_build", false, 1954461464216876662]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/pyo3-build-config-7c89f62fe735d8ba/dep-lib-pyo3_build_config", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}