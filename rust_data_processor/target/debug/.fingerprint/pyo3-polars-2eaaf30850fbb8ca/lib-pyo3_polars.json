{"rustc": 11410426090777951712, "features": "[]", "declared_features": "[\"ciborium\", \"derive\", \"lazy\", \"polars-ffi\", \"polars-lazy\", \"polars-plan\", \"pyo3-polars-derive\", \"serde\", \"serde-pickle\"]", "target": 14254807949758341313, "profile": 15657897354478470176, "path": 8652959428169410391, "deps": [[2597253916522265657, "polars", false, 1898595681503925379], [4446769819467865508, "polars_core", false, 8572961208274694123], [8008191657135824715, "thiserror", false, 1048344762606963720], [17396333227350152061, "pyo3", false, 14917536315963582420]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/pyo3-polars-2eaaf30850fbb8ca/dep-lib-pyo3_polars", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}