{"rustc": 11410426090777951712, "features": "[\"default\", \"extension-module\"]", "declared_features": "[\"abi3\", \"abi3-py310\", \"abi3-py311\", \"abi3-py312\", \"abi3-py37\", \"abi3-py38\", \"abi3-py39\", \"default\", \"extension-module\", \"generate-import-lib\"]", "target": 12398300617518054423, "profile": 17744942687396784568, "path": 1944763262605101476, "deps": [[4684437522915235464, "libc", false, 3936220782303720335], [7614379793313454108, "build_script_build", false, 8546844713599222096]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/pyo3-ffi-07bf85093e5f9df8/dep-lib-pyo3_ffi", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}