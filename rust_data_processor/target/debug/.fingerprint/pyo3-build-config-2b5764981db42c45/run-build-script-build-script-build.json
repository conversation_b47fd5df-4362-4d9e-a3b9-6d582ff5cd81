{"rustc": 11410426090777951712, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[11057642215492122650, "build_script_build", false, 10034639357849840011]], "local": [{"RerunIfEnvChanged": {"var": "PYO3_CONFIG_FILE", "val": null}}, {"RerunIfEnvChanged": {"var": "PYO3_NO_PYTHON", "val": null}}, {"RerunIfEnvChanged": {"var": "PYO3_ENVIRONMENT_SIGNATURE", "val": "cpython-3.11-64bit"}}, {"RerunIfEnvChanged": {"var": "PYO3_PYTHON", "val": "/home/<USER>/miniconda3/envs/data_auditing_env/bin/python"}}, {"RerunIfEnvChanged": {"var": "PYO3_USE_ABI3_FORWARD_COMPATIBILITY", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}