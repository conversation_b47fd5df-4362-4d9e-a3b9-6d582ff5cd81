import logging
import os
import glob
import re
import pandas as pd
from arcticdb import Arctic, QueryBuilder
from multiprocessing import Pool
import datetime
import sys

# Add the rust processor to the path
sys.path.append('/home/<USER>/repos/data_auditing/rust_data_processor')

# Import the optimized data processor - Rust only
from python_wrapper import process_and_resample_chunk

print("Using Rust-optimized data processing")

library = "nse/1_min/snap_file_jan20_may20/trd_ord"
sampled_location = f"{library.split('/')[2]}_sampled"
if not os.path.exists(f"/home/<USER>/repos/data_auditing/{sampled_location}"):
    os.makedirs(f"/home/<USER>/repos/data_auditing/{sampled_location}")
    
storet = Arctic("s3://*************:9000:arctic-db?access=super&secret=doopersecret")


def process_snap_file_optimized(sym):
    """
    Optimized version of process_snap_file using Rust backend for performance-critical operations.
    """
    try:
        if len(glob.glob(f"/home/<USER>/repos/data_auditing/{sampled_location}_combined/{sym}_*.parquet")):
            return

        store = Arctic(
            "s3://*************:9000:arctic-db?access=super&secret=doopersecret"
        )

        print(f"Started symbol: {sym}")
        lib = store[library]

        tot_len = lib.get_description(sym).row_count
        print(f"Total length for {sym} is {tot_len}")
        chunk_size = int(1e5)

        start = 0
        count = 0

        while start < tot_len:
            end = min(start + chunk_size, tot_len)

            print(f"Processing rows {start} to {end-1} of {tot_len}")

            df_chunk = lib.read(sym, row_range=[start, end]).data
            df_chunk = (
                df_chunk.drop_duplicates(keep="last")
                .sort_values(by="timestamp")
            )

            # Use optimized combined Rust processing - single pandas↔polars conversion
            # This does chunk separation AND resampling in one Rust call
            resampled_data = process_and_resample_chunk(df_chunk, interval_minutes=1)

            # Save resampled data directly - no additional processing needed
            if not resampled_data['fut_trd'].empty:
                resampled_data['fut_trd'].to_parquet(
                    f"/home/<USER>/repos/data_auditing/{sampled_location}/{sym}_fut_trd_{count}.parquet"
                )

            if not resampled_data['fut_ord'].empty:
                resampled_data['fut_ord'].to_parquet(
                    f"/home/<USER>/repos/data_auditing/{sampled_location}/{sym}_fut_ord_{count}.parquet"
                )

            if not resampled_data['opt_trd'].empty:
                resampled_data['opt_trd'].to_parquet(
                    f"/home/<USER>/repos/data_auditing/{sampled_location}/{sym}_opt_trd_{count}.parquet"
                )

            if not resampled_data['opt_ord'].empty:
                resampled_data['opt_ord'].to_parquet(
                    f"/home/<USER>/repos/data_auditing/{sampled_location}/{sym}_opt_ord_{count}.parquet"
                )

            count += 1
            start = end

        print(f"Completed symbol: {sym}\n")
    except Exception as e:
        print(f"Failed for {sym} due to: {e}\n")
        return


# Use the optimized function as the main processing function
process_snap_file = process_snap_file_optimized

# Rest of the original script remains the same
symbols = Arctic("s3://*************:9000:arctic-db?access=super&secret=doopersecret")[
    library
].list_symbols()

# The combined processing function remains unchanged
def combined_processed_snap_files(sym):
    print(f"Started combining {sym}...")

    df_fut_trd_list = [pd.DataFrame()]
    df_fut_ord_list = [pd.DataFrame()]
    df_opt_trd_list = [pd.DataFrame()]
    df_opt_ord_list = [pd.DataFrame()]

    for file in glob.glob(
        f"/home/<USER>/repos/data_auditing/{sampled_location}/{sym}_*.parquet"
    ):
        if "opt_trd" in file:
            df_opt_trd_list.append(pd.read_parquet(file))
        elif "opt_ord" in file:
            df_opt_ord_list.append(pd.read_parquet(file))
        elif "fut_trd" in file:
            df_fut_trd_list.append(pd.read_parquet(file))
        elif "fut_ord" in file:
            df_fut_ord_list.append(pd.read_parquet(file))
        

    # Combine and sort data - Rust functions already return sorted data
    df_fut_trd = pd.concat(df_fut_trd_list).sort_index()
    df_fut_ord = pd.concat(df_fut_ord_list).sort_index()
    df_opt_trd = pd.concat(df_opt_trd_list).sort_index()
    df_opt_ord = pd.concat(df_opt_ord_list).sort_index()

    df_fut_trd = df_fut_trd.rename(columns={"symbol": "ID", "open" : "Open", "high" : "High", "low" : "Low", "close" : "Close", "volume" : "Volume"})
    df_fut_ord = df_fut_ord.rename(columns={"symbol": "ID", "open" : "Open", "high" : "High", "low" : "Low", "close" : "Close"})
    df_opt_trd = df_opt_trd.rename(columns={"symbol": "ID", "open" : "Open", "high" : "High", "low" : "Low", "close" : "Close", "volume" : "Volume"})
    df_opt_ord = df_opt_ord.rename(columns={"symbol": "ID", "open" : "Open", "high" : "High", "low" : "Low", "close" : "Close"})


    if len(df_fut_trd):
        df_fut_trd.to_parquet(
            f"/home/<USER>/repos/data_auditing/{sampled_location}_combined/{sym}_fut_trd.parquet"
        )
    if len(df_fut_ord):
        df_fut_ord.to_parquet(
            f"/home/<USER>/repos/data_auditing/{sampled_location}_combined/{sym}_fut_ord.parquet"
        )
    if len(df_opt_trd):
        df_opt_trd.to_parquet(
            f"/home/<USER>/repos/data_auditing/{sampled_location}_combined/{sym}_opt_trd.parquet"
        )
    if len(df_opt_ord):
        df_opt_ord.to_parquet(
            f"/home/<USER>/repos/data_auditing/{sampled_location}_combined/{sym}_opt_ord.parquet"
        )

    for file in glob.glob(
        f"/home/<USER>/repos/data_auditing/{sampled_location}/{sym}_*.parquet"
    ):
        os.remove(file)

    print(f"Completed combining {sym}")


if __name__ == "__main__":
    # Example usage:
    process_snap_file("NIFTY")

    # For parallel processing:
    # with Pool(4) as p:
    #     p.map(process_snap_file, symbols)

    print("Using Rust-optimized data processing")
    print(f"Ready to process {len(symbols)} symbols")
    print("Use process_snap_file(symbol) to process individual symbols")
    print("Use combined_processed_snap_files(symbol) to combine processed files")
